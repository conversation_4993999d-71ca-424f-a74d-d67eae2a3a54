globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/utils/request.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                TokenManager: function() {
                    return TokenManager;
                },
                apiRequest: function() {
                    return apiRequest;
                },
                // 导出默认请求实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _umirequest = __mako_require__("node_modules/umi-request/dist/index.esm.js");
            var _apiErrorHandler = __mako_require__("src/utils/apiErrorHandler.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            // 创建请求实例
            const request = (0, _umirequest.extend)({
                prefix: '/api',
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            /**
 * Token 管理器（单令牌系统）
 *
 * 功能说明：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储
 * - 支持Token的设置、获取、清除和检查
 *
 * 设计理念：
 * - 采用单令牌系统，简化认证流程
 * - Token同时用于用户认证和团队访问
 * - 提供静态方法，便于全局调用
 */ class TokenManager {
                static TOKEN_KEY = 'auth_token';
                /**
   * 获取当前Token
   */ static getToken() {
                    return localStorage.getItem(TokenManager.TOKEN_KEY);
                }
                /**
   * 设置Token
   */ static setToken(token) {
                    localStorage.setItem(TokenManager.TOKEN_KEY, token);
                }
                /**
   * 清除Token
   */ static clearToken() {
                    localStorage.removeItem(TokenManager.TOKEN_KEY);
                }
                /**
   * 检查是否有Token
   */ static hasToken() {
                    return !!TokenManager.getToken();
                }
            }
            /**
 * 请求拦截器
 *
 * 功能：
 * - 自动在请求头中添加Authorization Bearer Token
 * - 统一处理认证信息的注入
 * - 支持无Token的公开接口访问
 */ request.interceptors.request.use((url, options)=>{
                const token = TokenManager.getToken();
                if (token) {
                    // 添加Authorization头部
                    const headers = {
                        ...options.headers,
                        Authorization: `Bearer ${token}`
                    };
                    return {
                        url,
                        options: {
                            ...options,
                            headers
                        }
                    };
                }
                return {
                    url,
                    options
                };
            });
            /**
 * 响应拦截器
 *
 * 功能：
 * - 统一处理API响应格式
 * - 自动处理认证失效情况
 * - 统一的错误消息提示
 * - 自动跳转到登录页面
 */ request.interceptors.response.use(async (response)=>{
                const data = await response.clone().json();
                // 使用新的错误处理器检查和处理API错误
                if (_apiErrorHandler.apiErrorHandler.isApiError(data)) {
                    const result = _apiErrorHandler.apiErrorHandler.handleApiResponse(data);
                    if (result && result.handled) // 错误已被处理，抛出错误以便调用方知道请求失败
                    return Promise.reject(new Error(data.message || '请求失败'));
                }
                return response;
            }, (error)=>{
                // 网络错误或其他错误
                if (error.response) {
                    const { status } = error.response;
                    if (status === 401) {
                        TokenManager.clearToken();
                        _antd.message.error('登录已过期，请重新登录');
                        if (window.location.pathname !== '/user/login') window.location.href = '/user/login';
                    } else if (status === 403) {
                        var _error_response_data, _error_response;
                        // 检查是否是团队访问被拒绝的特殊错误
                        const errorMessage = (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message;
                        if ((errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('停用')) || (errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('禁用')) || (errorMessage === null || errorMessage === void 0 ? void 0 : errorMessage.includes('不是该团队的成员'))) // 团队访问相关的错误，使用后端返回的具体消息
                        _antd.message.error(errorMessage);
                        else // 其他权限错误
                        _antd.message.error('没有权限访问该资源');
                    } else if (status === 404) _antd.message.error('请求的资源不存在');
                    else if (status >= 500) _antd.message.error('服务器错误，请稍后重试');
                    else _antd.message.error('请求失败');
                } else _antd.message.error('网络错误，请检查网络连接');
                return Promise.reject(error);
            });
            const apiRequest = {
                get: (url, params)=>{
                    return request.get(url, {
                        params
                    });
                },
                post: (url, data)=>{
                    return request.post(url, {
                        data
                    });
                },
                put: (url, data)=>{
                    return request.put(url, {
                        data
                    });
                },
                delete: (url, params)=>{
                    return request.delete(url, {
                        params
                    });
                }
            };
            var _default = request;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/utils/apiErrorHandler.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ApiErrorHandler: function() {
                    return ApiErrorHandler;
                },
                ErrorDisplayType: function() {
                    return ErrorDisplayType;
                },
                apiErrorHandler: function() {
                    return apiErrorHandler;
                },
                extractErrorInfo: function() {
                    return extractErrorInfo;
                },
                handleApiError: function() {
                    return handleApiError;
                },
                handleApiResponse: function() {
                    return handleApiResponse;
                },
                isApiError: function() {
                    return isApiError;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var ErrorDisplayType;
            (function(ErrorDisplayType) {
                /** 静默处理，不显示任何消息 */ ErrorDisplayType[ErrorDisplayType["SILENT"] = 0] = "SILENT";
                /** 显示警告消息 */ ErrorDisplayType[ErrorDisplayType["WARNING"] = 1] = "WARNING";
                /** 显示错误消息 */ ErrorDisplayType[ErrorDisplayType["ERROR"] = 2] = "ERROR";
                /** 显示通知 */ ErrorDisplayType[ErrorDisplayType["NOTIFICATION"] = 3] = "NOTIFICATION";
                /** 重定向（通常用于认证错误） */ ErrorDisplayType[ErrorDisplayType["REDIRECT"] = 9] = "REDIRECT";
            })(ErrorDisplayType || (ErrorDisplayType = {}));
            class ApiErrorHandler {
                static instance;
                defaultConfig = {
                    displayType: 2,
                    skipDefaultHandler: false,
                    logError: true
                };
                /**
   * 获取单例实例
   */ static getInstance() {
                    if (!ApiErrorHandler.instance) ApiErrorHandler.instance = new ApiErrorHandler();
                    return ApiErrorHandler.instance;
                }
                /**
   * 设置默认配置
   */ setDefaultConfig(config) {
                    this.defaultConfig = {
                        ...this.defaultConfig,
                        ...config
                    };
                }
                /**
   * 检查API响应是否包含错误
   */ isApiError(response) {
                    return (response === null || response === void 0 ? void 0 : response.code) !== undefined && response.code !== 200;
                }
                /**
   * 从API响应中提取错误信息
   */ extractErrorInfo(response) {
                    if (!this.isApiError(response)) return null;
                    return {
                        code: response.code,
                        message: response.message || '未知错误',
                        data: response.data,
                        timestamp: response.timestamp,
                        originalResponse: response
                    };
                }
                /**
   * 处理API错误
   */ handleError(errorInfo, config = {}) {
                    const finalConfig = {
                        ...this.defaultConfig,
                        ...config
                    };
                    // 记录错误日志
                    if (finalConfig.logError) console.error('API Error:', errorInfo);
                    // 如果有自定义处理器且跳过默认处理，则只执行自定义处理
                    if (finalConfig.customHandler && finalConfig.skipDefaultHandler) {
                        finalConfig.customHandler(errorInfo);
                        return {
                            handled: true,
                            message: 'Custom handler executed'
                        };
                    }
                    // 处理特殊错误代码
                    const specialHandlerResult = this.handleSpecialErrors(errorInfo);
                    if (specialHandlerResult.handled) return specialHandlerResult;
                    // 确定要显示的消息
                    const displayMessage = finalConfig.customMessage || errorInfo.message;
                    // 根据显示类型处理错误
                    const displayResult = this.displayError(displayMessage, finalConfig.displayType);
                    // 执行自定义处理器（如果存在且不跳过默认处理）
                    if (finalConfig.customHandler && !finalConfig.skipDefaultHandler) finalConfig.customHandler(errorInfo);
                    return {
                        handled: true,
                        message: displayMessage,
                        ...displayResult
                    };
                }
                /**
   * 处理特殊错误代码（认证、权限等）
   */ handleSpecialErrors(errorInfo) {
                    const { code, message: errorMessage } = errorInfo;
                    // 处理认证错误 (401)
                    if (code === 401) return this.handleAuthenticationError(errorMessage);
                    // 处理权限错误 (403)
                    if (code === 403) {
                        _antd.message.error(errorMessage || '没有权限访问该资源');
                        return {
                            handled: true,
                            message: errorMessage
                        };
                    }
                    // 处理服务器错误 (5xx)
                    if (code >= 500) {
                        const serverErrorMessage = '服务器内部错误，请稍后重试';
                        _antd.message.error(serverErrorMessage);
                        return {
                            handled: true,
                            message: serverErrorMessage
                        };
                    }
                    return {
                        handled: false
                    };
                }
                /**
   * 处理认证错误
   */ handleAuthenticationError(errorMessage) {
                    // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
                    const currentPath = window.location.pathname;
                    const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                    if (isDashboardRelated) {
                        console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
                        // 不立即清除Token和跳转，让页面自己处理
                        return {
                            handled: true,
                            message: '认证失败，请刷新页面重试'
                        };
                    }
                    // 其他页面立即处理认证错误
                    _services.AuthService.clearTokens();
                    _antd.message.error('登录已过期，请重新登录');
                    _max.history.push('/user/login');
                    return {
                        handled: true,
                        message: '登录已过期，请重新登录',
                        shouldRedirect: true,
                        redirectPath: '/user/login'
                    };
                }
                /**
   * 根据显示类型显示错误消息
   */ displayError(errorMessage, displayType = 2) {
                    switch(displayType){
                        case 0:
                            break;
                        case 1:
                            _antd.message.warning(errorMessage);
                            break;
                        case 2:
                            _antd.message.error(errorMessage);
                            break;
                        case 3:
                            _antd.notification.error({
                                message: '操作失败',
                                description: errorMessage,
                                duration: 4.5
                            });
                            break;
                        case 9:
                            break;
                        default:
                            _antd.message.error(errorMessage);
                            break;
                    }
                    return {};
                }
                /**
   * 便捷方法：处理API响应
   */ handleApiResponse(response, config) {
                    const errorInfo = this.extractErrorInfo(response);
                    if (!errorInfo) return null; // 没有错误
                    return this.handleError(errorInfo, config);
                }
                /**
   * 便捷方法：创建错误处理中间件
   */ createErrorMiddleware(config) {
                    return (response)=>{
                        this.handleApiResponse(response, config);
                        return response;
                    };
                }
            }
            const apiErrorHandler = ApiErrorHandler.getInstance();
            const handleApiError = (errorInfo, config)=>apiErrorHandler.handleError(errorInfo, config);
            const handleApiResponse = (response, config)=>apiErrorHandler.handleApiResponse(response, config);
            const isApiError = (response)=>apiErrorHandler.isApiError(response);
            const extractErrorInfo = (response)=>apiErrorHandler.extractErrorInfo(response);
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15714726011931733132';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.16173821640702684324.hot-update.js.map