/**
 * 错误处理器测试页面
 * 用于验证API错误处理器是否正常工作
 */

import React, { useState } from 'react';
import { Button, Card, Space, Typography, Divider } from 'antd';
import { apiRequest } from '@/services';
import { apiErrorHandler, ErrorDisplayType } from '@/utils/apiErrorHandler';

const { Title, Text } = Typography;

const TestErrorHandlerPage: React.FC = () => {
  const [loading, setLoading] = useState(false);

  // 测试自动错误处理
  const testAutoErrorHandling = async () => {
    setLoading(true);
    try {
      // 这个接口应该返回错误（假设不存在）
      await apiRequest.get('/test/error-endpoint');
    } catch (error) {
      console.log('请求失败，错误已自动处理');
    } finally {
      setLoading(false);
    }
  };

  // 测试手动错误处理
  const testManualErrorHandling = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test/error-endpoint');
      const data = await response.json();
      
      // 手动处理错误
      apiErrorHandler.handleApiResponse(data, {
        displayType: ErrorDisplayType.NOTIFICATION,
        customMessage: '这是一个自定义错误消息',
      });
    } catch (error) {
      console.log('手动错误处理测试完成');
    } finally {
      setLoading(false);
    }
  };

  // 测试不同的错误显示类型
  const testErrorDisplayTypes = () => {
    const mockErrorInfo = {
      code: 400,
      message: '测试错误消息',
      data: null,
    };

    // 测试警告消息
    apiErrorHandler.handleError(mockErrorInfo, {
      displayType: ErrorDisplayType.WARNING,
      customMessage: '这是一个警告消息',
    });

    setTimeout(() => {
      // 测试通知
      apiErrorHandler.handleError(mockErrorInfo, {
        displayType: ErrorDisplayType.NOTIFICATION,
        customMessage: '这是一个通知消息',
      });
    }, 1000);

    setTimeout(() => {
      // 测试错误消息
      apiErrorHandler.handleError(mockErrorInfo, {
        displayType: ErrorDisplayType.ERROR,
        customMessage: '这是一个错误消息',
      });
    }, 2000);
  };

  // 测试静默处理
  const testSilentHandling = () => {
    const mockErrorInfo = {
      code: 400,
      message: '这个错误不会显示',
      data: null,
    };

    apiErrorHandler.handleError(mockErrorInfo, {
      displayType: ErrorDisplayType.SILENT,
      customHandler: (errorInfo) => {
        console.log('静默处理的错误:', errorInfo);
      },
    });
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>API错误处理器测试页面</Title>
      <Text type="secondary">
        这个页面用于测试API错误处理器的各种功能。请打开浏览器控制台查看详细日志。
      </Text>

      <Divider />

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="自动错误处理测试" size="small">
          <Text>
            测试通过apiRequest调用不存在的接口，验证错误是否自动显示。
          </Text>
          <br />
          <br />
          <Button 
            type="primary" 
            onClick={testAutoErrorHandling}
            loading={loading}
          >
            测试自动错误处理
          </Button>
        </Card>

        <Card title="手动错误处理测试" size="small">
          <Text>
            测试手动调用错误处理器，使用自定义配置。
          </Text>
          <br />
          <br />
          <Button 
            onClick={testManualErrorHandling}
            loading={loading}
          >
            测试手动错误处理
          </Button>
        </Card>

        <Card title="错误显示类型测试" size="small">
          <Text>
            测试不同的错误显示类型：警告、通知、错误消息。
          </Text>
          <br />
          <br />
          <Button onClick={testErrorDisplayTypes}>
            测试错误显示类型
          </Button>
        </Card>

        <Card title="静默处理测试" size="small">
          <Text>
            测试静默错误处理，错误不会显示给用户，但会在控制台记录。
          </Text>
          <br />
          <br />
          <Button onClick={testSilentHandling}>
            测试静默处理
          </Button>
        </Card>
      </Space>

      <Divider />

      <Card title="使用说明" size="small">
        <Space direction="vertical">
          <Text>
            <strong>测试步骤：</strong>
          </Text>
          <Text>1. 点击各个测试按钮</Text>
          <Text>2. 观察页面上的错误消息显示</Text>
          <Text>3. 查看浏览器控制台的日志输出</Text>
          <Text>4. 验证错误处理器是否按预期工作</Text>
        </Space>
      </Card>
    </div>
  );
};

export default TestErrorHandlerPage;
