{"version": 3, "sources": ["umi.15714726011931733132.hot-update.js", "src/utils/apiErrorHandler.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='16326843690347009960';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * API错误处理工具类\n * \n * 功能特性：\n * 1. 自动检测API响应中的错误（code !== 200）\n * 2. 使用Ant Design的message组件显示错误消息\n * 3. 支持多种错误显示类型（静默、警告、错误、通知）\n * 4. 提供统一的错误处理接口\n * 5. 支持自定义错误处理逻辑\n * 6. 集成现有的认证和权限错误处理\n */\n\nimport { message, notification } from 'antd';\nimport { history } from '@umijs/max';\nimport { AuthService } from '@/services';\nimport type { ApiResponse } from '@/types/api';\n\n/**\n * 错误显示类型枚举\n */\nexport enum ErrorDisplayType {\n  /** 静默处理，不显示任何消息 */\n  SILENT = 0,\n  /** 显示警告消息 */\n  WARNING = 1,\n  /** 显示错误消息 */\n  ERROR = 2,\n  /** 显示通知 */\n  NOTIFICATION = 3,\n  /** 重定向（通常用于认证错误） */\n  REDIRECT = 9,\n}\n\n/**\n * 错误处理配置接口\n */\nexport interface ErrorHandlerConfig {\n  /** 错误显示类型 */\n  displayType?: ErrorDisplayType;\n  /** 自定义错误消息 */\n  customMessage?: string;\n  /** 是否跳过默认错误处理 */\n  skipDefaultHandler?: boolean;\n  /** 自定义错误处理函数 */\n  customHandler?: (error: ApiErrorInfo) => void;\n  /** 是否在控制台输出错误详情 */\n  logError?: boolean;\n}\n\n/**\n * API错误信息接口\n */\nexport interface ApiErrorInfo {\n  /** 错误代码 */\n  code: number;\n  /** 错误消息 */\n  message: string;\n  /** 错误数据 */\n  data?: any;\n  /** 时间戳 */\n  timestamp?: string;\n  /** 原始响应 */\n  originalResponse?: any;\n}\n\n/**\n * 错误处理结果接口\n */\nexport interface ErrorHandlerResult {\n  /** 是否已处理 */\n  handled: boolean;\n  /** 处理结果消息 */\n  message?: string;\n  /** 是否需要重定向 */\n  shouldRedirect?: boolean;\n  /** 重定向路径 */\n  redirectPath?: string;\n}\n\n/**\n * API错误处理器类\n */\nexport class ApiErrorHandler {\n  private static instance: ApiErrorHandler;\n  private defaultConfig: ErrorHandlerConfig = {\n    displayType: ErrorDisplayType.ERROR,\n    skipDefaultHandler: false,\n    logError: true,\n  };\n\n  /**\n   * 获取单例实例\n   */\n  public static getInstance(): ApiErrorHandler {\n    if (!ApiErrorHandler.instance) {\n      ApiErrorHandler.instance = new ApiErrorHandler();\n    }\n    return ApiErrorHandler.instance;\n  }\n\n  /**\n   * 设置默认配置\n   */\n  public setDefaultConfig(config: Partial<ErrorHandlerConfig>): void {\n    this.defaultConfig = { ...this.defaultConfig, ...config };\n  }\n\n  /**\n   * 检查API响应是否包含错误\n   */\n  public isApiError(response: any): boolean {\n    return response?.code !== undefined && response.code !== 200;\n  }\n\n  /**\n   * 从API响应中提取错误信息\n   */\n  public extractErrorInfo(response: any): ApiErrorInfo | null {\n    if (!this.isApiError(response)) {\n      return null;\n    }\n\n    return {\n      code: response.code,\n      message: response.message || '未知错误',\n      data: response.data,\n      timestamp: response.timestamp,\n      originalResponse: response,\n    };\n  }\n\n  /**\n   * 处理API错误\n   */\n  public handleError(\n    errorInfo: ApiErrorInfo,\n    config: ErrorHandlerConfig = {}\n  ): ErrorHandlerResult {\n    const finalConfig = { ...this.defaultConfig, ...config };\n\n    // 记录错误日志\n    if (finalConfig.logError) {\n      console.error('API Error:', errorInfo);\n      console.log('Error Handler Config:', finalConfig);\n    }\n\n    // 如果有自定义处理器且跳过默认处理，则只执行自定义处理\n    if (finalConfig.customHandler && finalConfig.skipDefaultHandler) {\n      finalConfig.customHandler(errorInfo);\n      return { handled: true, message: 'Custom handler executed' };\n    }\n\n    // 处理特殊错误代码\n    const specialHandlerResult = this.handleSpecialErrors(errorInfo);\n    if (specialHandlerResult.handled) {\n      return specialHandlerResult;\n    }\n\n    // 确定要显示的消息\n    const displayMessage = finalConfig.customMessage || errorInfo.message;\n\n    // 根据显示类型处理错误\n    const displayResult = this.displayError(displayMessage, finalConfig.displayType);\n\n    // 执行自定义处理器（如果存在且不跳过默认处理）\n    if (finalConfig.customHandler && !finalConfig.skipDefaultHandler) {\n      finalConfig.customHandler(errorInfo);\n    }\n\n    return {\n      handled: true,\n      message: displayMessage,\n      ...displayResult,\n    };\n  }\n\n  /**\n   * 处理特殊错误代码（认证、权限等）\n   */\n  private handleSpecialErrors(errorInfo: ApiErrorInfo): ErrorHandlerResult {\n    const { code, message: errorMessage } = errorInfo;\n\n    // 处理认证错误 (401)\n    if (code === 401) {\n      return this.handleAuthenticationError(errorMessage);\n    }\n\n    // 处理权限错误 (403)\n    if (code === 403) {\n      message.error(errorMessage || '没有权限访问该资源');\n      return { handled: true, message: errorMessage };\n    }\n\n    // 处理服务器错误 (5xx)\n    if (code >= 500) {\n      const serverErrorMessage = '服务器内部错误，请稍后重试';\n      message.error(serverErrorMessage);\n      return { handled: true, message: serverErrorMessage };\n    }\n\n    return { handled: false };\n  }\n\n  /**\n   * 处理认证错误\n   */\n  private handleAuthenticationError(errorMessage: string): ErrorHandlerResult {\n    // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题\n    const currentPath = window.location.pathname;\n    const isDashboardRelated =\n      currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');\n\n    if (isDashboardRelated) {\n      console.warn('Dashboard页面认证失败，可能是Token更新时序问题');\n      // 不立即清除Token和跳转，让页面自己处理\n      return { handled: true, message: '认证失败，请刷新页面重试' };\n    }\n\n    // 其他页面立即处理认证错误\n    AuthService.clearTokens();\n    message.error('登录已过期，请重新登录');\n    history.push('/user/login');\n\n    return {\n      handled: true,\n      message: '登录已过期，请重新登录',\n      shouldRedirect: true,\n      redirectPath: '/user/login',\n    };\n  }\n\n  /**\n   * 根据显示类型显示错误消息\n   */\n  private displayError(\n    errorMessage: string,\n    displayType: ErrorDisplayType = ErrorDisplayType.ERROR\n  ): Partial<ErrorHandlerResult> {\n    switch (displayType) {\n      case ErrorDisplayType.SILENT:\n        // 静默处理，不显示任何消息\n        break;\n\n      case ErrorDisplayType.WARNING:\n        message.warning(errorMessage);\n        break;\n\n      case ErrorDisplayType.ERROR:\n        message.error(errorMessage);\n        break;\n\n      case ErrorDisplayType.NOTIFICATION:\n        notification.error({\n          message: '操作失败',\n          description: errorMessage,\n          duration: 4.5,\n        });\n        break;\n\n      case ErrorDisplayType.REDIRECT:\n        // 重定向类型通常在特殊错误处理中处理\n        break;\n\n      default:\n        message.error(errorMessage);\n        break;\n    }\n\n    return {};\n  }\n\n  /**\n   * 便捷方法：处理API响应\n   */\n  public handleApiResponse<T = any>(\n    response: ApiResponse<T>,\n    config?: ErrorHandlerConfig\n  ): ErrorHandlerResult | null {\n    const errorInfo = this.extractErrorInfo(response);\n    if (!errorInfo) {\n      return null; // 没有错误\n    }\n\n    return this.handleError(errorInfo, config);\n  }\n\n  /**\n   * 便捷方法：创建错误处理中间件\n   */\n  public createErrorMiddleware(config?: ErrorHandlerConfig) {\n    return (response: any) => {\n      this.handleApiResponse(response, config);\n      return response;\n    };\n  }\n}\n\n// 导出单例实例\nexport const apiErrorHandler = ApiErrorHandler.getInstance();\n\n// 导出便捷函数\nexport const handleApiError = (\n  errorInfo: ApiErrorInfo,\n  config?: ErrorHandlerConfig\n) => apiErrorHandler.handleError(errorInfo, config);\n\nexport const handleApiResponse = <T = any>(\n  response: ApiResponse<T>,\n  config?: ErrorHandlerConfig\n) => apiErrorHandler.handleApiResponse(response, config);\n\nexport const isApiError = (response: any) => apiErrorHandler.isApiError(response);\n\nexport const extractErrorInfo = (response: any) =>\n  apiErrorHandler.extractErrorInfo(response);\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBC+EA,eAAe;2BAAf;;;;;gBAwNA,eAAe;2BAAf;;gBAeA,gBAAgB;2BAAhB;;gBAZA,cAAc;2BAAd;;gBAKA,iBAAiB;2BAAjB;;gBAKA,UAAU;2BAAV;;;;;yCA3SyB;wCACd;6CACI;;;;;;;;;;sBAMhB;gBACV,iBAAiB;gBAEjB,WAAW;gBAEX,WAAW;gBAEX,SAAS;gBAET,kBAAkB;eATR,qBAAA;YA8DL,MAAM;gBACX,OAAe,SAA0B;gBACjC,gBAAoC;oBAC1C,WAAW;oBACX,oBAAoB;oBACpB,UAAU;gBACZ,EAAE;gBAEF;;GAEC,GACD,OAAc,cAA+B;oBAC3C,IAAI,CAAC,gBAAgB,QAAQ,EAC3B,gBAAgB,QAAQ,GAAG,IAAI;oBAEjC,OAAO,gBAAgB,QAAQ;gBACjC;gBAEA;;GAEC,GACD,AAAO,iBAAiB,MAAmC,EAAQ;oBACjE,IAAI,CAAC,aAAa,GAAG;wBAAE,GAAG,IAAI,CAAC,aAAa;wBAAE,GAAG,MAAM;oBAAC;gBAC1D;gBAEA;;GAEC,GACD,AAAO,WAAW,QAAa,EAAW;oBACxC,OAAO,CAAA,qBAAA,+BAAA,SAAU,IAAI,MAAK,aAAa,SAAS,IAAI,KAAK;gBAC3D;gBAEA;;GAEC,GACD,AAAO,iBAAiB,QAAa,EAAuB;oBAC1D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,WACnB,OAAO;oBAGT,OAAO;wBACL,MAAM,SAAS,IAAI;wBACnB,SAAS,SAAS,OAAO,IAAI;wBAC7B,MAAM,SAAS,IAAI;wBACnB,WAAW,SAAS,SAAS;wBAC7B,kBAAkB;oBACpB;gBACF;gBAEA;;GAEC,GACD,AAAO,YACL,SAAuB,EACvB,SAA6B,CAAC,CAAC,EACX;oBACpB,MAAM,cAAc;wBAAE,GAAG,IAAI,CAAC,aAAa;wBAAE,GAAG,MAAM;oBAAC;oBAEvD,SAAS;oBACT,IAAI,YAAY,QAAQ,EAAE;wBACxB,QAAQ,KAAK,CAAC,cAAc;wBAC5B,QAAQ,GAAG,CAAC,yBAAyB;oBACvC;oBAEA,6BAA6B;oBAC7B,IAAI,YAAY,aAAa,IAAI,YAAY,kBAAkB,EAAE;wBAC/D,YAAY,aAAa,CAAC;wBAC1B,OAAO;4BAAE,SAAS;4BAAM,SAAS;wBAA0B;oBAC7D;oBAEA,WAAW;oBACX,MAAM,uBAAuB,IAAI,CAAC,mBAAmB,CAAC;oBACtD,IAAI,qBAAqB,OAAO,EAC9B,OAAO;oBAGT,WAAW;oBACX,MAAM,iBAAiB,YAAY,aAAa,IAAI,UAAU,OAAO;oBAErE,aAAa;oBACb,MAAM,gBAAgB,IAAI,CAAC,YAAY,CAAC,gBAAgB,YAAY,WAAW;oBAE/E,yBAAyB;oBACzB,IAAI,YAAY,aAAa,IAAI,CAAC,YAAY,kBAAkB,EAC9D,YAAY,aAAa,CAAC;oBAG5B,OAAO;wBACL,SAAS;wBACT,SAAS;wBACT,GAAG,aAAa;oBAClB;gBACF;gBAEA;;GAEC,GACD,AAAQ,oBAAoB,SAAuB,EAAsB;oBACvE,MAAM,EAAE,IAAI,EAAE,SAAS,YAAY,EAAE,GAAG;oBAExC,eAAe;oBACf,IAAI,SAAS,KACX,OAAO,IAAI,CAAC,yBAAyB,CAAC;oBAGxC,eAAe;oBACf,IAAI,SAAS,KAAK;wBAChB,aAAO,CAAC,KAAK,CAAC,gBAAgB;wBAC9B,OAAO;4BAAE,SAAS;4BAAM,SAAS;wBAAa;oBAChD;oBAEA,gBAAgB;oBAChB,IAAI,QAAQ,KAAK;wBACf,MAAM,qBAAqB;wBAC3B,aAAO,CAAC,KAAK,CAAC;wBACd,OAAO;4BAAE,SAAS;4BAAM,SAAS;wBAAmB;oBACtD;oBAEA,OAAO;wBAAE,SAAS;oBAAM;gBAC1B;gBAEA;;GAEC,GACD,AAAQ,0BAA0B,YAAoB,EAAsB;oBAC1E,0CAA0C;oBAC1C,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;oBAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBAAiB,YAAY,UAAU,CAAC;oBAEjE,IAAI,oBAAoB;wBACtB,QAAQ,IAAI,CAAC;wBACb,wBAAwB;wBACxB,OAAO;4BAAE,SAAS;4BAAM,SAAS;wBAAe;oBAClD;oBAEA,eAAe;oBACf,qBAAW,CAAC,WAAW;oBACvB,aAAO,CAAC,KAAK,CAAC;oBACd,YAAO,CAAC,IAAI,CAAC;oBAEb,OAAO;wBACL,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,cAAc;oBAChB;gBACF;gBAEA;;GAEC,GACD,AAAQ,aACN,YAAoB,EACpB,eAAsD,EACzB;oBAC7B,OAAQ;wBACN;4BAEE;wBAEF;4BACE,aAAO,CAAC,OAAO,CAAC;4BAChB;wBAEF;4BACE,aAAO,CAAC,KAAK,CAAC;4BACd;wBAEF;4BACE,kBAAY,CAAC,KAAK,CAAC;gCACjB,SAAS;gCACT,aAAa;gCACb,UAAU;4BACZ;4BACA;wBAEF;4BAEE;wBAEF;4BACE,aAAO,CAAC,KAAK,CAAC;4BACd;oBACJ;oBAEA,OAAO,CAAC;gBACV;gBAEA;;GAEC,GACD,AAAO,kBACL,QAAwB,EACxB,MAA2B,EACA;oBAC3B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;oBACxC,IAAI,CAAC,WACH,OAAO,MAAM,OAAO;oBAGtB,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW;gBACrC;gBAEA;;GAEC,GACD,AAAO,sBAAsB,MAA2B,EAAE;oBACxD,OAAO,CAAC;wBACN,IAAI,CAAC,iBAAiB,CAAC,UAAU;wBACjC,OAAO;oBACT;gBACF;YACF;YAGO,MAAM,kBAAkB,gBAAgB,WAAW;YAGnD,MAAM,iBAAiB,CAC5B,WACA,SACG,gBAAgB,WAAW,CAAC,WAAW;YAErC,MAAM,oBAAoB,CAC/B,UACA,SACG,gBAAgB,iBAAiB,CAAC,UAAU;YAE1C,MAAM,aAAa,CAAC,WAAkB,gBAAgB,UAAU,CAAC;YAEjE,MAAM,mBAAmB,CAAC,WAC/B,gBAAgB,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;IDvTrB;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}