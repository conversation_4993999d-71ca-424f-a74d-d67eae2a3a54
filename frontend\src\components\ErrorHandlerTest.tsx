/**
 * 错误处理器测试组件
 */

import React from 'react';
import { Button, Space, message } from 'antd';
import { apiErrorHandler, ErrorDisplayType } from '@/utils/apiErrorHandler';

const ErrorHandlerTest: React.FC = () => {
  
  // 测试基本错误处理
  const testBasicError = () => {
    console.log('=== 测试基本错误处理 ===');
    const mockResponse = {
      code: 400,
      message: '这是一个测试错误消息',
      data: null,
      timestamp: new Date().toISOString(),
    };

    const result = apiErrorHandler.handleApiResponse(mockResponse);
    console.log('处理结果:', result);
  };

  // 测试不同显示类型
  const testDisplayTypes = () => {
    console.log('=== 测试不同显示类型 ===');
    
    const mockError = {
      code: 400,
      message: '测试消息',
    };

    // 测试错误消息
    setTimeout(() => {
      apiErrorHandler.handleError(mockError, {
        displayType: ErrorDisplayType.ERROR,
        customMessage: '错误类型消息',
      });
    }, 0);

    // 测试警告消息
    setTimeout(() => {
      apiErrorHandler.handleError(mockError, {
        displayType: ErrorDisplayType.WARNING,
        customMessage: '警告类型消息',
      });
    }, 1000);

    // 测试通知
    setTimeout(() => {
      apiErrorHandler.handleError(mockError, {
        displayType: ErrorDisplayType.NOTIFICATION,
        customMessage: '通知类型消息',
      });
    }, 2000);
  };

  // 测试静默处理
  const testSilentHandling = () => {
    console.log('=== 测试静默处理 ===');
    
    const mockError = {
      code: 400,
      message: '静默处理的错误',
    };

    apiErrorHandler.handleError(mockError, {
      displayType: ErrorDisplayType.SILENT,
      customHandler: (errorInfo) => {
        console.log('静默处理回调:', errorInfo);
        message.info('静默处理完成（通过自定义处理器显示）');
      },
    });
  };

  // 测试成功响应
  const testSuccessResponse = () => {
    console.log('=== 测试成功响应 ===');
    
    const successResponse = {
      code: 200,
      message: 'success',
      data: { result: 'ok' },
      timestamp: new Date().toISOString(),
    };

    const result = apiErrorHandler.handleApiResponse(successResponse);
    console.log('成功响应处理结果:', result);
    
    if (result === null) {
      message.success('成功响应正确处理（未触发错误处理）');
    }
  };

  // 测试错误检测
  const testErrorDetection = () => {
    console.log('=== 测试错误检测 ===');
    
    const testCases = [
      { code: 200, message: 'success' },
      { code: 400, message: 'bad request' },
      { code: 401, message: 'unauthorized' },
      { code: 403, message: 'forbidden' },
      { code: 500, message: 'server error' },
      { success: false, errorMessage: 'old format' },
      { data: 'no code field' },
    ];

    testCases.forEach((testCase, index) => {
      const isError = apiErrorHandler.isApiError(testCase);
      console.log(`测试用例 ${index + 1}:`, testCase, '是否为错误:', isError);
    });
  };

  return (
    <div style={{ padding: 16 }}>
      <h3>错误处理器测试</h3>
      <Space wrap>
        <Button onClick={testBasicError}>
          测试基本错误处理
        </Button>
        <Button onClick={testDisplayTypes}>
          测试显示类型
        </Button>
        <Button onClick={testSilentHandling}>
          测试静默处理
        </Button>
        <Button onClick={testSuccessResponse}>
          测试成功响应
        </Button>
        <Button onClick={testErrorDetection}>
          测试错误检测
        </Button>
      </Space>
      <div style={{ marginTop: 16, fontSize: 12, color: '#666' }}>
        请打开浏览器控制台查看详细日志
      </div>
    </div>
  );
};

export default ErrorHandlerTest;
