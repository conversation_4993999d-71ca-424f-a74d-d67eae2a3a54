globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/utils/apiErrorHandler.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                ApiErrorHandler: function() {
                    return ApiErrorHandler;
                },
                ErrorDisplayType: function() {
                    return ErrorDisplayType;
                },
                apiErrorHandler: function() {
                    return apiErrorHandler;
                },
                extractErrorInfo: function() {
                    return extractErrorInfo;
                },
                handleApiError: function() {
                    return handleApiError;
                },
                handleApiResponse: function() {
                    return handleApiResponse;
                },
                isApiError: function() {
                    return isApiError;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _services = __mako_require__("src/services/index.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var ErrorDisplayType;
            (function(ErrorDisplayType) {
                /** 静默处理，不显示任何消息 */ ErrorDisplayType[ErrorDisplayType["SILENT"] = 0] = "SILENT";
                /** 显示警告消息 */ ErrorDisplayType[ErrorDisplayType["WARNING"] = 1] = "WARNING";
                /** 显示错误消息 */ ErrorDisplayType[ErrorDisplayType["ERROR"] = 2] = "ERROR";
                /** 显示通知 */ ErrorDisplayType[ErrorDisplayType["NOTIFICATION"] = 3] = "NOTIFICATION";
                /** 重定向（通常用于认证错误） */ ErrorDisplayType[ErrorDisplayType["REDIRECT"] = 9] = "REDIRECT";
            })(ErrorDisplayType || (ErrorDisplayType = {}));
            class ApiErrorHandler {
                static instance;
                defaultConfig = {
                    displayType: 2,
                    skipDefaultHandler: false,
                    logError: true
                };
                /**
   * 获取单例实例
   */ static getInstance() {
                    if (!ApiErrorHandler.instance) ApiErrorHandler.instance = new ApiErrorHandler();
                    return ApiErrorHandler.instance;
                }
                /**
   * 设置默认配置
   */ setDefaultConfig(config) {
                    this.defaultConfig = {
                        ...this.defaultConfig,
                        ...config
                    };
                }
                /**
   * 检查API响应是否包含错误
   */ isApiError(response) {
                    return (response === null || response === void 0 ? void 0 : response.code) !== undefined && response.code !== 200;
                }
                /**
   * 从API响应中提取错误信息
   */ extractErrorInfo(response) {
                    if (!this.isApiError(response)) return null;
                    return {
                        code: response.code,
                        message: response.message || '未知错误',
                        data: response.data,
                        timestamp: response.timestamp,
                        originalResponse: response
                    };
                }
                /**
   * 处理API错误
   */ handleError(errorInfo, config = {}) {
                    const finalConfig = {
                        ...this.defaultConfig,
                        ...config
                    };
                    // 记录错误日志
                    if (finalConfig.logError) {
                        console.error('API Error:', errorInfo);
                        console.log('Error Handler Config:', finalConfig);
                    }
                    // 如果有自定义处理器且跳过默认处理，则只执行自定义处理
                    if (finalConfig.customHandler && finalConfig.skipDefaultHandler) {
                        finalConfig.customHandler(errorInfo);
                        return {
                            handled: true,
                            message: 'Custom handler executed'
                        };
                    }
                    // 处理特殊错误代码
                    const specialHandlerResult = this.handleSpecialErrors(errorInfo);
                    if (specialHandlerResult.handled) return specialHandlerResult;
                    // 确定要显示的消息
                    const displayMessage = finalConfig.customMessage || errorInfo.message;
                    // 根据显示类型处理错误
                    const displayResult = this.displayError(displayMessage, finalConfig.displayType);
                    // 执行自定义处理器（如果存在且不跳过默认处理）
                    if (finalConfig.customHandler && !finalConfig.skipDefaultHandler) finalConfig.customHandler(errorInfo);
                    return {
                        handled: true,
                        message: displayMessage,
                        ...displayResult
                    };
                }
                /**
   * 处理特殊错误代码（认证、权限等）
   */ handleSpecialErrors(errorInfo) {
                    const { code, message: errorMessage } = errorInfo;
                    // 处理认证错误 (401)
                    if (code === 401) return this.handleAuthenticationError(errorMessage);
                    // 处理权限错误 (403)
                    if (code === 403) {
                        _antd.message.error(errorMessage || '没有权限访问该资源');
                        return {
                            handled: true,
                            message: errorMessage
                        };
                    }
                    // 处理服务器错误 (5xx)
                    if (code >= 500) {
                        const serverErrorMessage = '服务器内部错误，请稍后重试';
                        _antd.message.error(serverErrorMessage);
                        return {
                            handled: true,
                            message: serverErrorMessage
                        };
                    }
                    return {
                        handled: false
                    };
                }
                /**
   * 处理认证错误
   */ handleAuthenticationError(errorMessage) {
                    // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
                    const currentPath = window.location.pathname;
                    const isDashboardRelated = currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');
                    if (isDashboardRelated) {
                        console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
                        // 不立即清除Token和跳转，让页面自己处理
                        return {
                            handled: true,
                            message: '认证失败，请刷新页面重试'
                        };
                    }
                    // 其他页面立即处理认证错误
                    _services.AuthService.clearTokens();
                    _antd.message.error('登录已过期，请重新登录');
                    _max.history.push('/user/login');
                    return {
                        handled: true,
                        message: '登录已过期，请重新登录',
                        shouldRedirect: true,
                        redirectPath: '/user/login'
                    };
                }
                /**
   * 根据显示类型显示错误消息
   */ displayError(errorMessage, displayType = 2) {
                    console.log('Displaying error:', {
                        errorMessage,
                        displayType
                    });
                    switch(displayType){
                        case 0:
                            break;
                        case 1:
                            _antd.message.warning(errorMessage);
                            break;
                        case 2:
                            _antd.message.error(errorMessage);
                            break;
                        case 3:
                            _antd.notification.error({
                                message: '操作失败',
                                description: errorMessage,
                                duration: 4.5
                            });
                            break;
                        case 9:
                            break;
                        default:
                            _antd.message.error(errorMessage);
                            break;
                    }
                    return {};
                }
                /**
   * 便捷方法：处理API响应
   */ handleApiResponse(response, config) {
                    const errorInfo = this.extractErrorInfo(response);
                    if (!errorInfo) return null; // 没有错误
                    return this.handleError(errorInfo, config);
                }
                /**
   * 便捷方法：创建错误处理中间件
   */ createErrorMiddleware(config) {
                    return (response)=>{
                        this.handleApiResponse(response, config);
                        return response;
                    };
                }
            }
            const apiErrorHandler = ApiErrorHandler.getInstance();
            const handleApiError = (errorInfo, config)=>apiErrorHandler.handleError(errorInfo, config);
            const handleApiResponse = (response, config)=>apiErrorHandler.handleApiResponse(response, config);
            const isApiError = (response)=>apiErrorHandler.isApiError(response);
            const extractErrorInfo = (response)=>apiErrorHandler.extractErrorInfo(response);
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '473089776530298132';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.16326843690347009960.hot-update.js.map