/**
 * API错误处理工具类
 * 
 * 功能特性：
 * 1. 自动检测API响应中的错误（code !== 200）
 * 2. 使用Ant Design的message组件显示错误消息
 * 3. 支持多种错误显示类型（静默、警告、错误、通知）
 * 4. 提供统一的错误处理接口
 * 5. 支持自定义错误处理逻辑
 * 6. 集成现有的认证和权限错误处理
 */

import { message, notification } from 'antd';
import { history } from '@umijs/max';
import { AuthService } from '@/services';
import type { ApiResponse } from '@/types/api';

/**
 * 错误显示类型枚举
 */
export enum ErrorDisplayType {
  /** 静默处理，不显示任何消息 */
  SILENT = 0,
  /** 显示警告消息 */
  WARNING = 1,
  /** 显示错误消息 */
  ERROR = 2,
  /** 显示通知 */
  NOTIFICATION = 3,
  /** 重定向（通常用于认证错误） */
  REDIRECT = 9,
}

/**
 * 错误处理配置接口
 */
export interface ErrorHandlerConfig {
  /** 错误显示类型 */
  displayType?: ErrorDisplayType;
  /** 自定义错误消息 */
  customMessage?: string;
  /** 是否跳过默认错误处理 */
  skipDefaultHandler?: boolean;
  /** 自定义错误处理函数 */
  customHandler?: (error: ApiErrorInfo) => void;
  /** 是否在控制台输出错误详情 */
  logError?: boolean;
}

/**
 * API错误信息接口
 */
export interface ApiErrorInfo {
  /** 错误代码 */
  code: number;
  /** 错误消息 */
  message: string;
  /** 错误数据 */
  data?: any;
  /** 时间戳 */
  timestamp?: string;
  /** 原始响应 */
  originalResponse?: any;
}

/**
 * 错误处理结果接口
 */
export interface ErrorHandlerResult {
  /** 是否已处理 */
  handled: boolean;
  /** 处理结果消息 */
  message?: string;
  /** 是否需要重定向 */
  shouldRedirect?: boolean;
  /** 重定向路径 */
  redirectPath?: string;
}

/**
 * API错误处理器类
 */
export class ApiErrorHandler {
  private static instance: ApiErrorHandler;
  private defaultConfig: ErrorHandlerConfig = {
    displayType: ErrorDisplayType.ERROR,
    skipDefaultHandler: false,
    logError: true,
  };

  /**
   * 获取单例实例
   */
  public static getInstance(): ApiErrorHandler {
    if (!ApiErrorHandler.instance) {
      ApiErrorHandler.instance = new ApiErrorHandler();
    }
    return ApiErrorHandler.instance;
  }

  /**
   * 设置默认配置
   */
  public setDefaultConfig(config: Partial<ErrorHandlerConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }

  /**
   * 检查API响应是否包含错误
   */
  public isApiError(response: any): boolean {
    return response?.code !== undefined && response.code !== 200;
  }

  /**
   * 从API响应中提取错误信息
   */
  public extractErrorInfo(response: any): ApiErrorInfo | null {
    if (!this.isApiError(response)) {
      return null;
    }

    return {
      code: response.code,
      message: response.message || '未知错误',
      data: response.data,
      timestamp: response.timestamp,
      originalResponse: response,
    };
  }

  /**
   * 处理API错误
   */
  public handleError(
    errorInfo: ApiErrorInfo,
    config: ErrorHandlerConfig = {}
  ): ErrorHandlerResult {
    const finalConfig = { ...this.defaultConfig, ...config };

    // 记录错误日志
    if (finalConfig.logError) {
      console.error('API Error:', errorInfo);
      console.log('Error Handler Config:', finalConfig);
    }

    // 如果有自定义处理器且跳过默认处理，则只执行自定义处理
    if (finalConfig.customHandler && finalConfig.skipDefaultHandler) {
      finalConfig.customHandler(errorInfo);
      return { handled: true, message: 'Custom handler executed' };
    }

    // 处理特殊错误代码
    const specialHandlerResult = this.handleSpecialErrors(errorInfo);
    if (specialHandlerResult.handled) {
      return specialHandlerResult;
    }

    // 确定要显示的消息
    const displayMessage = finalConfig.customMessage || errorInfo.message;

    // 根据显示类型处理错误
    const displayResult = this.displayError(displayMessage, finalConfig.displayType);

    // 执行自定义处理器（如果存在且不跳过默认处理）
    if (finalConfig.customHandler && !finalConfig.skipDefaultHandler) {
      finalConfig.customHandler(errorInfo);
    }

    return {
      handled: true,
      message: displayMessage,
      ...displayResult,
    };
  }

  /**
   * 处理特殊错误代码（认证、权限等）
   */
  private handleSpecialErrors(errorInfo: ApiErrorInfo): ErrorHandlerResult {
    const { code, message: errorMessage } = errorInfo;

    // 处理认证错误 (401)
    if (code === 401) {
      return this.handleAuthenticationError(errorMessage);
    }

    // 处理权限错误 (403)
    if (code === 403) {
      message.error(errorMessage || '没有权限访问该资源');
      return { handled: true, message: errorMessage };
    }

    // 处理服务器错误 (5xx)
    if (code >= 500) {
      const serverErrorMessage = '服务器内部错误，请稍后重试';
      message.error(serverErrorMessage);
      return { handled: true, message: serverErrorMessage };
    }

    return { handled: false };
  }

  /**
   * 处理认证错误
   */
  private handleAuthenticationError(errorMessage: string): ErrorHandlerResult {
    // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
    const currentPath = window.location.pathname;
    const isDashboardRelated =
      currentPath.startsWith('/dashboard') || currentPath.startsWith('/team');

    if (isDashboardRelated) {
      console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
      // 不立即清除Token和跳转，让页面自己处理
      return { handled: true, message: '认证失败，请刷新页面重试' };
    }

    // 其他页面立即处理认证错误
    AuthService.clearTokens();
    message.error('登录已过期，请重新登录');
    history.push('/user/login');

    return {
      handled: true,
      message: '登录已过期，请重新登录',
      shouldRedirect: true,
      redirectPath: '/user/login',
    };
  }

  /**
   * 根据显示类型显示错误消息
   */
  private displayError(
    errorMessage: string,
    displayType: ErrorDisplayType = ErrorDisplayType.ERROR
  ): Partial<ErrorHandlerResult> {
    console.log('Displaying error:', { errorMessage, displayType });
    switch (displayType) {
      case ErrorDisplayType.SILENT:
        // 静默处理，不显示任何消息
        break;

      case ErrorDisplayType.WARNING:
        message.warning(errorMessage);
        break;

      case ErrorDisplayType.ERROR:
        message.error(errorMessage);
        break;

      case ErrorDisplayType.NOTIFICATION:
        notification.error({
          message: '操作失败',
          description: errorMessage,
          duration: 4.5,
        });
        break;

      case ErrorDisplayType.REDIRECT:
        // 重定向类型通常在特殊错误处理中处理
        break;

      default:
        message.error(errorMessage);
        break;
    }

    return {};
  }

  /**
   * 便捷方法：处理API响应
   */
  public handleApiResponse<T = any>(
    response: ApiResponse<T>,
    config?: ErrorHandlerConfig
  ): ErrorHandlerResult | null {
    const errorInfo = this.extractErrorInfo(response);
    if (!errorInfo) {
      return null; // 没有错误
    }

    return this.handleError(errorInfo, config);
  }

  /**
   * 便捷方法：创建错误处理中间件
   */
  public createErrorMiddleware(config?: ErrorHandlerConfig) {
    return (response: any) => {
      this.handleApiResponse(response, config);
      return response;
    };
  }
}

// 导出单例实例
export const apiErrorHandler = ApiErrorHandler.getInstance();

// 导出便捷函数
export const handleApiError = (
  errorInfo: ApiErrorInfo,
  config?: ErrorHandlerConfig
) => apiErrorHandler.handleError(errorInfo, config);

export const handleApiResponse = <T = any>(
  response: ApiResponse<T>,
  config?: ErrorHandlerConfig
) => apiErrorHandler.handleApiResponse(response, config);

export const isApiError = (response: any) => apiErrorHandler.isApiError(response);

export const extractErrorInfo = (response: any) =>
  apiErrorHandler.extractErrorInfo(response);
